using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

public class UIFollowDistance : MonoBehaviour {
    [SerializeField]
    private Transform targetTransform;
    [SerializeField]
    private float minDistance;
    [SerializeField]
    private float maxDistance;
    [SerializeField]
    private UIFollowDistanceAnimationCurve animationCurve ;
    [SerializeField]
    private float fadeTime = 0.5f;

    private float sqrtMinDistance;
    private float sqrtMaxDistance;
    private float offset;
    private Vector3 lastTargetPos;
    private Vector3 lastCameraPos;
    private Canvas canvas;
    private int sortingOrderBegin;
    private Vector3 localScale = Vector3.zero;
    private float timer = -1f;
    private float fadeTimeMultiplier = 0f;
    private int updateNumber = 0;

    private static int totalCount = 0;
    
    void Start()
    {
        this.sqrtMinDistance = this.minDistance * this.minDistance;
        this.sqrtMaxDistance = this.maxDistance * this.maxDistance;
        this.offset = this.maxDistance - this.minDistance;
        fadeTimeMultiplier = fadeTime != 0 ? 1 / fadeTime : fadeTimeMultiplier;

        canvas = this.transform.GetComponent<Canvas>();
        Canvas parent_canvas = this.transform.parent.GetComponent<Canvas>();
        if (null!= parent_canvas) sortingOrderBegin = parent_canvas.sortingOrder;
    }

    void OnEnable()
    {
        ++totalCount;
        updateNumber = totalCount;
        this.lastTargetPos = Vector3.zero;
        this.lastCameraPos = Vector3.zero;
    }

    void OnDisable()
    {
        --totalCount;
        timer = -1f;
    }

    private void Update()
    {
        --updateNumber;
        if (updateNumber <= 0)
        {
            UpdateDistance();
            updateNumber = totalCount;
        }

        if (timer > 0)
        {
            timer -= Time.deltaTime;
            transform.localScale = Vector3.Slerp(transform.localScale, localScale, fadeTimeMultiplier * (fadeTime - timer));
        }
    }

    public void UpdateDistance()
    {
        if (null != this.targetTransform && null != Camera.main)
        {
            if (this.lastTargetPos != this.targetTransform.position || this.lastCameraPos != Camera.main.transform.position)
            {
                this.lastTargetPos = this.targetTransform.position;
                this.lastCameraPos = Camera.main.transform.position;
                this.UpdateScale();
            }
        }
        else
        {
            localScale.x = 1;
            localScale.y = 1;
            localScale.z = 1;
            this.transform.localScale = localScale;
            timer = -1f;
        }
    }

    private void UpdateScale(bool imminently = false)
    {
        if (null != this.targetTransform && null != Camera.main)
        {
            float lastScale = this.transform.localScale.x;

            var sqrtDistance = (this.targetTransform.position - Camera.main.transform.position).sqrMagnitude;
            if (!this.IsVisible()|| sqrtDistance > sqrtMaxDistance)
            {
                localScale.x = 0;
                localScale.y = 0;
                localScale.z = 0;
                this.transform.localScale = localScale;
                timer = -1;
            }
            else if (sqrtDistance <= this.sqrtMinDistance)
            {
                localScale.x = 1;
                localScale.y = 1;
                localScale.z = 1;
                this.transform.localScale = localScale;
                timer = -1f;
            }
            else
            {
                var distance = Mathf.Sqrt(sqrtDistance) - this.minDistance;
                var scale = Mathf.Clamp(animationCurve.Curve.Evaluate(distance / this.offset), 0f, 1f);
                if (localScale.x == 0)
                {
                    imminently = true;
                }
                localScale.x = scale;
                localScale.y = scale;
                localScale.z = scale;
                if (imminently)
                {
                    this.transform.localScale = localScale;
                    timer = -1f;
                }
                else
                {
                    if (timer <= 0)
                    {
                        timer = fadeTime;
                    }
                }
            }

            if (null != canvas && Mathf.Abs(this.transform.localScale.x - lastScale) >= 0.001f)
            {
                canvas.overrideSorting = true;
                canvas.sortingOrder = sortingOrderBegin + (int)(this.transform.localScale.x * 1000.0f);
            }
        }
    }

    // 是否在视野内
    private bool IsVisible()
    {
        var visible = true;
        if (null != this.targetTransform && null != Camera.main)
        {
            var vector = Camera.main.transform.position - this.targetTransform.position;
            if (Vector3.Dot(vector, Camera.main.transform.forward) > 0)
                visible = false;
        }
        return visible;
    }

    public float MinDistance
    {
        set
        {
            if (this.minDistance != value)
            {
                this.minDistance = value;
                this.sqrtMinDistance = this.minDistance * this.minDistance;
                this.offset = this.maxDistance - this.minDistance;
                this.UpdateScale();
            }
        }
        get { return this.minDistance; }
    }

    public float MaxDistance
    {
        set
        {
            if (this.maxDistance != value)
            {
                this.maxDistance = value;
                this.sqrtMaxDistance = this.maxDistance * this.maxDistance;
                this.offset = this.maxDistance - this.minDistance;
                this.UpdateScale();
            }
        }
        get { return this.maxDistance; }
    }

    public Transform TargetTransform
    {
        set
        {
            this.targetTransform = value;
            if (null != this.targetTransform)
            {
                this.lastTargetPos = this.targetTransform.position;
                this.UpdateScale(true);
            }
        }
        get { return this.targetTransform; }
    }
}
